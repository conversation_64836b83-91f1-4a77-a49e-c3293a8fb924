// import mongoose
const mongoose = require("mongoose");
 
// route handler
const commentSchemas = new mongoose.Schema({
    post:{
        type:mongoose.Schema.Types.ObjectId,
        ref:"Post", // this is the reference to the postModel
        required:true,
    },
    user:{
        type: String,
        required: true,
    },
    body: {
        type:String,
        required: true,
    }
})


// exports
module.exports = mongoose.model("Comment", commentSchemas);