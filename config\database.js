const mongoose = require("mongoose");
require("dotenv").config();

const connectDB = async () => {
        const conn = await mongoose.connect(process.env.DATABASE_URL, {
            userNewUrlParser: true,
            useUnifiedTopology: true,
        }).then(console.log("DB connected successfully"))
        .catch((error)=>{
            console.log(error);
            process.exit(1);
        })
}
module.exports = connectDB;
